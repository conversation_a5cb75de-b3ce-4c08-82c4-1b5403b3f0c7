// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
//@version=5

strategy("IB Breakout Strategy", shorttitle="IB Strat", overlay=true, default_qty_type=strategy.percent_of_equity, default_qty_value=10)

daySess = input.session("0930-1600","Session Period",options=["0930-1600","0700-1030","0720-1030","0915-1530"])
ibSess = input.session("0929-1029", "Initial Balance", options=["0929-1029", "0930-0945", "0930-1000", "0930-1030","0700-0800","0720-0930","1330-1600","0200-0500","1200-1330","0915-0945"])
show_extension = input.bool(true,"Show Extension","Display extended bars on the second day")

// Strategy inputs
use_stop_loss = input.bool(true, "Use Stop Loss", "Enable stop loss at opposite IB level")
risk_reward_ratio = input.float(2.0, "Risk/Reward Ratio", minval=0.5, maxval=5.0, tooltip="Risk reward ratio for position sizing")
max_trades_per_day = input.int(2, "Max Trades Per Day", minval=1, maxval=10, tooltip="Maximum number of trades per day")

// Signal toggle inputs
enable_first_trade = input.bool(true, "Enable First Trade", "Enable the initial IB mid signal at 10:29am", group="Signal Controls")
enable_second_trade = input.bool(true, "Enable Second Trade", "Enable the inverse trade after first trade stops out", group="Signal Controls")
enable_third_trade = input.bool(true, "Enable Third Trade", "Enable the third inverse trade after 11:30am (only if second trade stops out)", group="Signal Controls")

//Bars
is_newbar(sess) =>
    t = time("D", sess, "America/New_York")
    na(t[1]) and not na(t) or t[1] < t

is_session(sess) =>
    not na(time(timeframe.period, sess, "America/New_York"))

nyNewbar = is_newbar(daySess)

bool inInitialBalance = is_session(ibSess)
int ib_opening_bar = na
ib_opening_bar := ib_opening_bar[1]

float ib_high = na
float ib_low = na

float last_ib_high = na
float last_ib_low = na


if inInitialBalance and not inInitialBalance[1]
    last_ib_high := na
    last_ib_low := na
    ib_high := na
    ib_low := na
    ib_opening_bar := bar_index

else if inInitialBalance and inInitialBalance[1] and not inInitialBalance[2]
    last_ib_high := ib_high[3]
    last_ib_low := ib_low[3]
    ib_high := math.max(high,high[1])
    ib_low := math.min(low,low[1])

else if inInitialBalance
    ib_high := math.max(ib_high[1],high)
    ib_low := math.min(ib_low[1],low)
    last_ib_high := last_ib_high[1]
    last_ib_low := last_ib_low[1]

else
    ib_high := ib_high[1]
    ib_low := ib_low[1]
    last_ib_high := last_ib_high[1]
    last_ib_low := last_ib_low[1]


ib_high_line = plot(ib_high,"Initial Balance High",color.gray,2,style=plot.style_linebr)
ib_low_line = plot(ib_low,"Initial Balance Low",color.gray,2,style=plot.style_linebr)

fill(ib_high_line,ib_low_line,color.new(color.gray,95))

// Calculate and plot developing midpoint
float ib_mid = not na(ib_high) and not na(ib_low) ? (ib_high + ib_low)/2 : na

// Calculate 25%, 50%, 75% levels within the IB range
float ib_25_level = not na(ib_high) and not na(ib_low) ? ib_low + ((ib_high - ib_low) * 0.25) : na
float ib_75_level = not na(ib_high) and not na(ib_low) ? ib_low + ((ib_high - ib_low) * 0.75) : na

// Variable to store the color of IB mid line
var color ib_mid_color = color.white

// Reset color to white at the start of each new IB session
if inInitialBalance and not inInitialBalance[1]
    ib_mid_color := color.white

// Check if we're at the end of IB session (10:29am candle) and determine color
if inInitialBalance[1] and not inInitialBalance
    // This is the first bar after IB session ends (10:29am candle just closed)
    if not na(ib_mid) and not na(close[1])
        if close[1] > ib_mid
            ib_mid_color := color.green
        else if close[1] < ib_mid
            ib_mid_color := color.red
        else
            ib_mid_color := color.white

ib_mid_line = plot(ib_mid,"Initial Balance Midpoint (50%)",ib_mid_color,1,style=plot.style_linebr)
ib_25_line = plot(ib_25_level,"Initial Balance 25%",color.new(color.blue, 50),1,style=plot.style_linebr)
ib_75_line = plot(ib_75_level,"Initial Balance 75%",color.new(color.blue, 50),1,style=plot.style_linebr)

// Add blue fills
fill(ib_25_line, ib_low_line, color.new(#1a388b, 95), title="IB Low to 25% Fill")
fill(ib_75_line, ib_high_line, color.new(#1a388b, 95), title="IB 75% to High Fill")

last_ib_high_line = plot(show_extension?last_ib_high:na,"Last Initial Balance High",color.gray,1,style=plot.style_linebr)
last_ib_low_line = plot(show_extension?last_ib_low:na,"Last Initial Balance Low",color.gray,1,style=plot.style_linebr,display=display.price_scale)

// Variables to store labels
var label ib_high_label = na
var label ib_low_label = na
var label ib_mid_label = na
var label ib_25_label = na
var label ib_75_label = na
var label last_ib_high_label = na
var label last_ib_low_label = na

// Add labels for Initial Balance lines
if barstate.islast
    // Delete previous labels to prevent snail trail
    if not na(ib_high_label)
        label.delete(ib_high_label)
    if not na(ib_low_label)
        label.delete(ib_low_label)
    if not na(ib_mid_label)
        label.delete(ib_mid_label)
    if not na(ib_25_label)
        label.delete(ib_25_label)
    if not na(ib_75_label)
        label.delete(ib_75_label)
    if not na(last_ib_high_label)
        label.delete(last_ib_high_label)
    if not na(last_ib_low_label)
        label.delete(last_ib_low_label)

    // Create new labels
    if not na(ib_high)
        ib_high_label := label.new(bar_index, ib_high, "IB High", style=label.style_label_left, textcolor=color.gray, size=size.large, color=color.new(color.white, 100))

    if not na(ib_low)
        ib_low_label := label.new(bar_index, ib_low, "IB Low", style=label.style_label_left, textcolor=color.gray, size=size.large, color=color.new(color.white, 100))

    if not na(ib_mid)
        ib_mid_label := label.new(bar_index, ib_mid, "IB Mid (50%)", style=label.style_label_left, textcolor=ib_mid_color, size=size.large, color=color.new(color.white, 100))

    if not na(ib_25_level)
        ib_25_label := label.new(bar_index, ib_25_level, "IB 25%", style=label.style_label_left, textcolor=color.new(color.blue, 50), size=size.large, color=color.new(color.white, 100))

    if not na(ib_75_level)
        ib_75_label := label.new(bar_index, ib_75_level, "IB 75%", style=label.style_label_left, textcolor=color.new(color.blue, 50), size=size.large, color=color.new(color.white, 100))

    if show_extension and not na(last_ib_high)
        last_ib_high_label := label.new(bar_index, last_ib_high, "Last IB High", style=label.style_label_left, textcolor=color.gray, size=size.large, color=color.new(color.white, 100))

    if show_extension and not na(last_ib_low)
        last_ib_low_label := label.new(bar_index, last_ib_low, "Last IB Low", style=label.style_label_left, textcolor=color.gray, size=size.large, color=color.new(color.white, 100))

// Vertical lines at 9:29, 10:29, and 11:30
var line start_line = na
var line end_line = na
var line hour_line = na

// Check for 11:30 time
bool is1130 = hour(time, "America/New_York") == 11 and minute(time, "America/New_York") == 30

if inInitialBalance and not inInitialBalance[1]
    // Draw vertical line at 9:29 (start of initial balance)
    start_line := line.new(bar_index, low, bar_index, high, extend=extend.both, color=color.white, style=line.style_dotted, width=1)

if inInitialBalance[1] and not inInitialBalance
    // Draw vertical line at 10:29 (end of initial balance)
    end_line := line.new(bar_index, low, bar_index, high, extend=extend.both, color=color.white, style=line.style_dotted, width=1)

if is1130 and not is1130[1]
    // Draw vertical line at 11:30
    hour_line := line.new(bar_index, low, bar_index, high, extend=extend.both, color=color.white, style=line.style_dotted, width=1)

// Calculate IB range and extension levels
float ib_range = not na(ib_high) and not na(ib_low) ? ib_high - ib_low : na

// Calculate 25%, 50%, and 100% extensions above IB high
float ib_high_25_ext = not na(ib_high) and not na(ib_range) ? ib_high + (ib_range * 0.25) : na
float ib_high_50_ext = not na(ib_high) and not na(ib_range) ? ib_high + (ib_range * 0.50) : na
float ib_high_100_ext = not na(ib_high) and not na(ib_range) ? ib_high + (ib_range * 1.00) : na

// Calculate 25%, 50%, and 100% extensions below IB low
float ib_low_25_ext = not na(ib_low) and not na(ib_range) ? ib_low - (ib_range * 0.25) : na
float ib_low_50_ext = not na(ib_low) and not na(ib_range) ? ib_low - (ib_range * 0.50) : na
float ib_low_100_ext = not na(ib_low) and not na(ib_range) ? ib_low - (ib_range * 1.00) : na

// Plot extension lines
ib_high_25_ext_line = plot(ib_high_25_ext, "IB High 25% Extension", color.new(color.gray, 60), 1, style=plot.style_linebr)
ib_high_50_ext_line = plot(ib_high_50_ext, "IB High 50% Extension", color.new(color.gray, 40), 1, style=plot.style_linebr)
ib_high_100_ext_line = plot(ib_high_100_ext, "IB High 100% Extension", color.new(color.gray, 20), 1, style=plot.style_linebr)
ib_low_25_ext_line = plot(ib_low_25_ext, "IB Low 25% Extension", color.new(color.gray, 60), 1, style=plot.style_linebr)
ib_low_50_ext_line = plot(ib_low_50_ext, "IB Low 50% Extension", color.new(color.gray, 40), 1, style=plot.style_linebr)
ib_low_100_ext_line = plot(ib_low_100_ext, "IB Low 100% Extension", color.new(color.gray, 20), 1, style=plot.style_linebr)

// Variables to store extension labels
var label ib_high_25_label = na
var label ib_high_50_label = na
var label ib_high_100_label = na
var label ib_low_25_label = na
var label ib_low_50_label = na
var label ib_low_100_label = na

// Add labels for extension lines
if barstate.islast
    // Delete previous extension labels to prevent snail trail
    if not na(ib_high_25_label)
        label.delete(ib_high_25_label)
    if not na(ib_high_50_label)
        label.delete(ib_high_50_label)
    if not na(ib_high_100_label)
        label.delete(ib_high_100_label)
    if not na(ib_low_25_label)
        label.delete(ib_low_25_label)
    if not na(ib_low_50_label)
        label.delete(ib_low_50_label)
    if not na(ib_low_100_label)
        label.delete(ib_low_100_label)

    // Create new extension labels
    if not na(ib_high_25_ext)
        ib_high_25_label := label.new(bar_index, ib_high_25_ext, "IB High +25%", style=label.style_label_left, textcolor=color.new(color.gray, 60), size=size.large, color=color.new(color.white, 100))

    if not na(ib_high_50_ext)
        ib_high_50_label := label.new(bar_index, ib_high_50_ext, "IB High +50%", style=label.style_label_left, textcolor=color.new(color.gray, 40), size=size.large, color=color.new(color.white, 100))

    if not na(ib_high_100_ext)
        ib_high_100_label := label.new(bar_index, ib_high_100_ext, "IB High +100%", style=label.style_label_left, textcolor=color.new(color.gray, 20), size=size.large, color=color.new(color.white, 100))

    if not na(ib_low_25_ext)
        ib_low_25_label := label.new(bar_index, ib_low_25_ext, "IB Low -25%", style=label.style_label_left, textcolor=color.new(color.gray, 60), size=size.large, color=color.new(color.white, 100))

    if not na(ib_low_50_ext)
        ib_low_50_label := label.new(bar_index, ib_low_50_ext, "IB Low -50%", style=label.style_label_left, textcolor=color.new(color.gray, 40), size=size.large, color=color.new(color.white, 100))

    if not na(ib_low_100_ext)
        ib_low_100_label := label.new(bar_index, ib_low_100_ext, "IB Low -100%", style=label.style_label_left, textcolor=color.new(color.gray, 20), size=size.large, color=color.new(color.white, 100))

// ============================================================================
// STRATEGY LOGIC
// ============================================================================

// Track daily trade count and first trade outcome
var int daily_trade_count = 0
var int last_trade_day = 0
var bool first_trade_was_long = na
var bool first_trade_was_short = na
var bool first_trade_stopped_out = false
var bool second_trade_taken = false
var bool second_trade_stopped_out = false
var bool second_trade_won = false
var bool third_trade_taken = false

// Reset daily trade count and first trade tracking on new day
if nyNewbar
    daily_trade_count := 0
    last_trade_day := dayofweek
    first_trade_was_long := na
    first_trade_was_short := na
    first_trade_stopped_out := false
    second_trade_taken := false
    second_trade_stopped_out := false
    second_trade_won := false
    third_trade_taken := false

// Strategy conditions
bool ib_session_ended = inInitialBalance[1] and not inInitialBalance
bool can_trade = daily_trade_count < max_trades_per_day and not na(ib_high) and not na(ib_low) and not na(ib_high_25_ext) and not na(ib_low_25_ext)
bool after_ib_session = not inInitialBalance and not na(ib_high) and not na(ib_low)

// Time condition for third trade (after 11:30am EST)
bool after_1130 = hour(time, "America/New_York") > 11 or (hour(time, "America/New_York") == 11 and minute(time, "America/New_York") >= 30)

// Second trade conditions - inverse of IB mid color after first trade stops out
bool can_take_second_trade = first_trade_stopped_out and not second_trade_taken and daily_trade_count == 1 and strategy.position_size == 0

// Third trade conditions - inverse of IB mid color but only after 11:30am and only if second trade didn't win
bool can_take_third_trade = second_trade_stopped_out and not second_trade_won and not third_trade_taken and daily_trade_count == 2 and strategy.position_size == 0 and after_1130

// Variables to store entry levels and exit states
var float long_entry_price = na
var float short_entry_price = na
var float long_entry_candle_low = na
var float short_entry_candle_high = na
var bool long_breakeven_triggered = false
var bool long_25_exit_triggered = false
var bool long_50_exit_triggered = false
var bool short_breakeven_triggered = false
var bool short_25_exit_triggered = false
var bool short_50_exit_triggered = false
var bool long_keep_original_stop = false
var bool short_keep_original_stop = false

// 10:29 IB Mid Signal - New signals at end of IB session
bool ib_mid_long_signal = enable_first_trade and ib_session_ended and not na(ib_mid) and close[1] > ib_mid and can_trade and strategy.position_size == 0
bool ib_mid_short_signal = enable_first_trade and ib_session_ended and not na(ib_mid) and close[1] < ib_mid and can_trade and strategy.position_size == 0

// Second trade signals - inverse of IB mid color (price direction at 10:29am)
bool second_trade_short_signal = enable_second_trade and can_take_second_trade and ib_mid_color == color.green and close < ib_mid and close[1] >= ib_mid
bool second_trade_long_signal = enable_second_trade and can_take_second_trade and ib_mid_color == color.red and close > ib_mid and close[1] <= ib_mid

// Third trade signals - same as second trade but only after 11:30am (inverse of IB mid color)
bool third_trade_short_signal = enable_third_trade and can_take_third_trade and ib_mid_color == color.green and close < ib_mid and close[1] >= ib_mid
bool third_trade_long_signal = enable_third_trade and can_take_third_trade and ib_mid_color == color.red and close > ib_mid and close[1] <= ib_mid

// Reset exit state variables on new day
if nyNewbar
    long_breakeven_triggered := false
    long_25_exit_triggered := false
    long_50_exit_triggered := false
    short_breakeven_triggered := false
    short_25_exit_triggered := false
    short_50_exit_triggered := false

// Entry logic
// 10:29 IB Mid signals (first trade)
if ib_mid_long_signal
    strategy.entry("Long Mid", strategy.long, comment="IB Mid Long")
    long_entry_price := close
    long_entry_candle_low := low  // Store entry candle low for initial stop loss
    // Reset exit states for new position
    long_breakeven_triggered := false
    long_25_exit_triggered := false
    long_50_exit_triggered := false
    // Check if signal occurred in upper portion of IB range (75% to high)
    long_keep_original_stop := (close[1] >= ib_75_level and close[1] <= ib_high) ? true : false
    daily_trade_count := daily_trade_count + 1
    // Track first trade direction
    first_trade_was_long := true
    first_trade_was_short := false

if ib_mid_short_signal
    strategy.entry("Short Mid", strategy.short, comment="IB Mid Short")
    short_entry_price := close
    short_entry_candle_high := high  // Store entry candle high for initial stop loss
    // Reset exit states for new position
    short_breakeven_triggered := false
    short_25_exit_triggered := false
    short_50_exit_triggered := false
    // Check if signal occurred in lower portion of IB range (25% to low)
    short_keep_original_stop := (close[1] <= ib_25_level and close[1] >= ib_low) ? true : false
    daily_trade_count := daily_trade_count + 1
    // Track first trade direction
    first_trade_was_short := true
    first_trade_was_long := false

// Second trade entries (inverse of IB mid color)
if second_trade_long_signal
    strategy.entry("Long Second", strategy.long, comment="Second Trade Long")
    long_entry_price := close
    long_entry_candle_low := low  // Store entry candle low for initial stop loss
    // Reset exit states for new position
    long_breakeven_triggered := false
    long_25_exit_triggered := false
    long_50_exit_triggered := false
    long_keep_original_stop := false  // Always move to break even at IB high/low for second trades
    daily_trade_count := daily_trade_count + 1
    second_trade_taken := true

if second_trade_short_signal
    strategy.entry("Short Second", strategy.short, comment="Second Trade Short")
    short_entry_price := close
    short_entry_candle_high := high  // Store entry candle high for initial stop loss
    // Reset exit states for new position
    short_breakeven_triggered := false
    short_25_exit_triggered := false
    short_50_exit_triggered := false
    short_keep_original_stop := false  // Always move to break even at IB high/low for second trades
    daily_trade_count := daily_trade_count + 1
    second_trade_taken := true

// Third trade entries (inverse of IB mid color, same as second trade but after 11:30am)
if third_trade_long_signal
    strategy.entry("Long Third", strategy.long, comment="Third Trade Long")
    long_entry_price := close
    long_entry_candle_low := low  // Store entry candle low for initial stop loss
    // Reset exit states for new position
    long_breakeven_triggered := false
    long_25_exit_triggered := false
    long_50_exit_triggered := false
    long_keep_original_stop := false  // Always move to break even at IB high/low for third trades
    daily_trade_count := daily_trade_count + 1
    third_trade_taken := true

if third_trade_short_signal
    strategy.entry("Short Third", strategy.short, comment="Third Trade Short")
    short_entry_price := close
    short_entry_candle_high := high  // Store entry candle high for initial stop loss
    // Reset exit states for new position
    short_breakeven_triggered := false
    short_25_exit_triggered := false
    short_50_exit_triggered := false
    short_keep_original_stop := false  // Always move to break even at IB high/low for third trades
    daily_trade_count := daily_trade_count + 1
    third_trade_taken := true

// Track when first trade gets stopped out
if strategy.position_size == 0 and strategy.position_size[1] != 0 and daily_trade_count == 1 and not first_trade_stopped_out
    first_trade_stopped_out := true

// Track when second trade gets stopped out
if strategy.position_size == 0 and strategy.position_size[1] != 0 and daily_trade_count == 2 and not second_trade_stopped_out
    second_trade_stopped_out := true

// Exit logic - Fixed stop loss management
// Long position management
if strategy.position_size > 0 and not na(long_entry_price) and not na(long_entry_candle_low)
    // Initial stop loss at entry candle low with 2 point buffer
    initial_stop = long_entry_candle_low - 2

    // Move stop to break even when price touches IB high (unless signal was in upper IB range)
    if high >= ib_high and not long_breakeven_triggered and not long_keep_original_stop
        long_breakeven_triggered := true

    // Move stop to break even when price touches 25% extension (for signals in upper IB range)
    if high >= ib_high_25_ext and not long_breakeven_triggered and long_keep_original_stop
        long_breakeven_triggered := true

    // Determine current stop loss level based on break even status
    current_stop = long_breakeven_triggered ? long_entry_price : initial_stop

    // Set stop loss for first trade
    if not second_trade_taken
        strategy.exit("Long Stop", "Long Mid", stop=current_stop, comment=long_breakeven_triggered ? "Break Even Stop" : "Initial Stop")
    else if second_trade_taken and not third_trade_taken
        // Second trade exits - different logic
        if daily_trade_count == 2
            // For second trade: stop at break even when touching IB low, initial stop at crossing candle high + 2pt buffer
            second_trade_stop = long_breakeven_triggered ? long_entry_price : initial_stop
            strategy.exit("Long Stop", "Long Second", stop=second_trade_stop, comment=long_breakeven_triggered ? "Break Even Stop" : "Initial Stop")
    else if third_trade_taken
        // Third trade exits - same logic as second trade
        if daily_trade_count == 3
            // For third trade: stop at break even when touching IB low, initial stop at crossing candle high + 2pt buffer
            third_trade_stop = long_breakeven_triggered ? long_entry_price : initial_stop
            strategy.exit("Long Stop", "Long Third", stop=third_trade_stop, comment=long_breakeven_triggered ? "Break Even Stop" : "Initial Stop")

    // Take profit logic for first trade
    if not second_trade_taken
        // Take 80% profit at 25% extension
        if close >= ib_high_25_ext and not long_25_exit_triggered
            strategy.close("Long Mid", qty_percent=80, comment="80% Profit at 25%")
            long_25_exit_triggered := true

        // Take 50% of remaining (10% of original) at 50% extension
        if close >= ib_high_50_ext and long_25_exit_triggered and not long_50_exit_triggered
            strategy.close("Long Mid", qty_percent=50, comment="50% Remaining at 50%")
            long_50_exit_triggered := true

        // Take final position at 100% extension
        if close >= ib_high_100_ext and long_50_exit_triggered
            strategy.close("Long Mid", qty_percent=100, comment="Final Exit at 100%")
    else if second_trade_taken and not third_trade_taken
        // Second trade: Take 100% profit at 25% extension
        if close >= ib_high_25_ext
            strategy.close("Long Second", qty_percent=100, comment="Second Trade Exit at 25%")
            second_trade_won := true
    else if third_trade_taken
        // Third trade: Take 100% profit at 25% extension
        if close >= ib_high_25_ext
            strategy.close("Long Third", qty_percent=100, comment="Third Trade Exit at 25%")

// Short position management
if strategy.position_size < 0 and not na(short_entry_price) and not na(short_entry_candle_high)
    // Initial stop loss at entry candle high with 2 point buffer
    initial_stop = short_entry_candle_high + 2

    // Move stop to break even when price touches IB low (unless signal was in lower IB range)
    if low <= ib_low and not short_breakeven_triggered and not short_keep_original_stop
        short_breakeven_triggered := true

    // Move stop to break even when price touches 25% extension (for signals in lower IB range)
    if low <= ib_low_25_ext and not short_breakeven_triggered and short_keep_original_stop
        short_breakeven_triggered := true

    // Determine current stop loss level based on break even status
    current_stop = short_breakeven_triggered ? short_entry_price : initial_stop

    // Set stop loss for first trade
    if not second_trade_taken
        strategy.exit("Short Stop", "Short Mid", stop=current_stop, comment=short_breakeven_triggered ? "Break Even Stop" : "Initial Stop")
    else if second_trade_taken and not third_trade_taken
        // Second trade exits - different logic
        if daily_trade_count == 2
            // For second trade: stop at break even when touching IB high, initial stop at crossing candle low - 2pt buffer
            second_trade_stop = short_breakeven_triggered ? short_entry_price : initial_stop
            strategy.exit("Short Stop", "Short Second", stop=second_trade_stop, comment=short_breakeven_triggered ? "Break Even Stop" : "Initial Stop")
    else if third_trade_taken
        // Third trade exits - same logic as second trade
        if daily_trade_count == 3
            // For third trade: stop at break even when touching IB high, initial stop at crossing candle low - 2pt buffer
            third_trade_stop = short_breakeven_triggered ? short_entry_price : initial_stop
            strategy.exit("Short Stop", "Short Third", stop=third_trade_stop, comment=short_breakeven_triggered ? "Break Even Stop" : "Initial Stop")

    // Take profit logic for first trade
    if not second_trade_taken
        // Take 80% profit at 25% extension
        if close <= ib_low_25_ext and not short_25_exit_triggered
            strategy.close("Short Mid", qty_percent=80, comment="80% Profit at 25%")
            short_25_exit_triggered := true

        // Take 50% of remaining (10% of original) at 50% extension
        if close <= ib_low_50_ext and short_25_exit_triggered and not short_50_exit_triggered
            strategy.close("Short Mid", qty_percent=50, comment="50% Remaining at 50%")
            short_50_exit_triggered := true

        // Take final position at 100% extension
        if close <= ib_low_100_ext and short_50_exit_triggered
            strategy.close("Short Mid", qty_percent=100, comment="Final Exit at 100%")
    else if second_trade_taken and not third_trade_taken
        // Second trade: Take 100% profit at 25% extension
        if close <= ib_low_25_ext
            strategy.close("Short Second", qty_percent=100, comment="Second Trade Exit at 25%")
            second_trade_won := true
    else if third_trade_taken
        // Third trade: Take 100% profit at 25% extension
        if close <= ib_low_25_ext
            strategy.close("Short Third", qty_percent=100, comment="Third Trade Exit at 25%")

// Trade signals are automatically shown by strategy entries/exits
// Removed plotshape to avoid duplicate signals

// Plot strategy info
var table info_table = table.new(position.top_right, 2, 10, bgcolor=color.white, border_width=1)
if barstate.islast
    table.cell(info_table, 0, 0, "Daily Trades:", text_color=color.black)
    table.cell(info_table, 1, 0, str.tostring(daily_trade_count) + "/" + str.tostring(max_trades_per_day), text_color=color.black)
    table.cell(info_table, 0, 1, "Position:", text_color=color.black)
    table.cell(info_table, 1, 1, strategy.position_size > 0 ? "Long" : strategy.position_size < 0 ? "Short" : "Flat", text_color=color.black)
    table.cell(info_table, 0, 2, "IB Range:", text_color=color.black)
    table.cell(info_table, 1, 2, not na(ib_range) ? str.tostring(ib_range, "#.##") : "N/A", text_color=color.black)
    table.cell(info_table, 0, 3, "Exit Status:", text_color=color.black)
    exit_status = strategy.position_size > 0 ? (long_50_exit_triggered ? "Final 10%" : long_25_exit_triggered ? "20% Left" : "Full Position") : strategy.position_size < 0 ? (short_50_exit_triggered ? "Final 10%" : short_25_exit_triggered ? "20% Left" : "Full Position") : "No Position"
    table.cell(info_table, 1, 3, exit_status, text_color=color.black)
    table.cell(info_table, 0, 4, "First Trade:", text_color=color.black)
    first_trade_status = first_trade_was_long ? "Long" : first_trade_was_short ? "Short" : "None"
    table.cell(info_table, 1, 4, first_trade_status, text_color=color.black)
    table.cell(info_table, 0, 5, "Second Trade:", text_color=color.black)
    second_trade_status = not enable_second_trade ? "Disabled" : first_trade_stopped_out ? (second_trade_taken ? "Taken" : "Waiting") : "N/A"
    table.cell(info_table, 1, 5, second_trade_status, text_color=color.black)
    table.cell(info_table, 0, 6, "Third Trade:", text_color=color.black)
    third_trade_status = not enable_third_trade ? "Disabled" : second_trade_won ? "Blocked (2nd Won)" : second_trade_stopped_out ? (third_trade_taken ? "Taken" : after_1130 ? "Waiting" : "Wait 11:30") : "N/A"
    table.cell(info_table, 1, 6, third_trade_status, text_color=color.black)

    // Signal enable/disable status
    table.cell(info_table, 0, 7, "1st Signal:", text_color=color.black)
    table.cell(info_table, 1, 7, enable_first_trade ? "Enabled" : "Disabled", text_color=enable_first_trade ? color.green : color.red)
    table.cell(info_table, 0, 8, "2nd Signal:", text_color=color.black)
    table.cell(info_table, 1, 8, enable_second_trade ? "Enabled" : "Disabled", text_color=enable_second_trade ? color.green : color.red)
    table.cell(info_table, 0, 9, "3rd Signal:", text_color=color.black)
    table.cell(info_table, 1, 9, enable_third_trade ? "Enabled" : "Disabled", text_color=enable_third_trade ? color.green : color.red)
